{"__meta": {"id": "01JWZMRRK1SD1XG3CA5V6DTPQX", "datetime": "2025-06-05 14:54:20", "utime": **********.004039, "method": "POST", "uri": "/daily_break/start", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749113649.058695, "end": **********.004067, "duration": 10.945371866226196, "duration_str": "10.95s", "measures": [{"label": "Booting", "start": 1749113649.058695, "relative_start": 0, "end": **********.004892, "relative_end": **********.004892, "duration": 1.****************, "duration_str": "1.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.004921, "relative_start": 1.***************, "end": **********.004071, "relative_end": 4.0531158447265625e-06, "duration": 8.***************, "duration_str": "9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.062372, "relative_start": 2.****************, "end": **********.07652, "relative_end": **********.07652, "duration": 0.*****************, "duration_str": "14.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.996759, "relative_start": 10.***************, "end": **********.997733, "relative_end": **********.997733, "duration": 0.0009741783142089844, "duration_str": "974μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST daily_break/start", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Daily Break Create", "controller": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.daily_break.start", "prefix": "/daily_break", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php:75-90</a>"}, "queries": {"count": 16, "nb_statements": 14, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10743, "accumulated_duration_str": "107ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.228771, "duration": 0.01726, "duration_str": "17.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 16.066}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.271363, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 16.066, "width_percent": 0.791}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.287902, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 16.857, "width_percent": 0.773}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.2943032, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 17.63, "width_percent": 1.257}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3193061, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 18.887, "width_percent": 1.257}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.328059, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 20.143, "width_percent": 1.275}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.357774, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 21.419, "width_percent": 1.322}, {"sql": "select * from `attendances` where `user_id` = 1 and `type` = 'Regular' and `clock_out` is null and `attendances`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "Regular"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 23}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3770409, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:23", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=23", "ajax": false, "filename": "BreakStartStopService.php", "line": "23"}, "connection": "blueorange", "explain": null, "start_percent": 22.74, "width_percent": 1.378}, {"sql": "select * from `employee_shifts` where `employee_shifts`.`user_id` = 1 and `employee_shifts`.`user_id` is not null and `status` = 'active' and `employee_shifts`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 69}, {"index": 25, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 135}, {"index": 26, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 33}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.390467, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=69", "ajax": false, "filename": "UserAccessors.php", "line": "69"}, "connection": "blueorange", "explain": null, "start_percent": 24.118, "width_percent": 2.113}, {"sql": "select * from `attendances` where `attendances`.`user_id` = 1 and `attendances`.`user_id` is not null and `type` = 'Regular' and `clock_out` is null and `attendances`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "Regular"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 143}, {"index": 20, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.398499, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:143", "source": {"index": 19, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=143", "ajax": false, "filename": "BreakStartStopService.php", "line": "143"}, "connection": "blueorange", "explain": null, "start_percent": 26.231, "width_percent": 1.666}, {"sql": "select count(*) as aggregate from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `type` = 'Short' and `daily_breaks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 5074, "Short"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 181}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.408458, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=181", "ajax": false, "filename": "BreakStartStopService.php", "line": "181"}, "connection": "blueorange", "explain": null, "start_percent": 27.897, "width_percent": 1.108}, {"sql": "select count(*) as aggregate from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `type` = 'Long' and `daily_breaks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 5074, "<PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 186}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.417183, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:186", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=186", "ajax": false, "filename": "BreakStartStopService.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 29.005, "width_percent": 1.033}, {"sql": "select * from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `daily_breaks`.`deleted_at` is null order by `break_out_at` desc limit 1", "type": "query", "params": [], "bindings": [1, 5074], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 39}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.423321, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:202", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=202", "ajax": false, "filename": "BreakStartStopService.php", "line": "202"}, "connection": "blueorange", "explain": null, "start_percent": 30.038, "width_percent": 1.182}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 45}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.189637, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:45", "source": {"index": 10, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=45", "ajax": false, "filename": "BreakStartStopService.php", "line": "45"}, "connection": "blueorange", "explain": null, "start_percent": 31.22, "width_percent": 0}, {"sql": "insert into `daily_breaks` (`user_id`, `attendance_id`, `date`, `break_in_at`, `break_in_ip`, `type`, `updated_at`, `created_at`) values (1, 5074, '2025-06-05 00:00:00', '2025-06-05 14:54:11', '*************', 'Short', '2025-06-05 14:54:19', '2025-06-05 14:54:19')", "type": "query", "params": [], "bindings": [1, 5074, "2025-06-05 00:00:00", "2025-06-05 14:54:11", "*************", "Short", "2025-06-05 14:54:19", "2025-06-05 14:54:19"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 46}, {"index": 25, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 45}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.195632, "duration": 0.07389, "duration_str": "73.89ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:46", "source": {"index": 21, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=46", "ajax": false, "filename": "BreakStartStopService.php", "line": "46"}, "connection": "blueorange", "explain": null, "start_percent": 31.22, "width_percent": 68.78}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 45}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.321546, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:45", "source": {"index": 9, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=45", "ajax": false, "filename": "BreakStartStopService.php", "line": "45"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\Attendance\\Attendance": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAttendance.php&line=1", "ajax": false, "filename": "Attendance.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\EmployeeShift\\EmployeeShift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FEmployeeShift%2FEmployeeShift.php&line=1", "ajax": false, "filename": "EmployeeShift.php", "line": "?"}}}, "count": 9, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1074786921 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074786921\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.371796, "xdebug_link": null}]}, "session": {"_token": "WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/daily_break/start_stop\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1748856962\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Break Started Successfully.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/daily_break/start", "action_name": "administration.daily_break.start", "controller_action": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak", "uri": "POST daily_break/start", "controller": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/daily_break", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php:75-90</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Daily Break Create", "duration": "10.95s", "peak_memory": "30MB", "response": "Redirect to https://blueorange.test/daily_break/start_stop", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1536350546 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536350546\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1546246184 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>userid</span>\" => \"<span class=sf-dump-str title=\"11 characters\">UID00000001</span>\"\n  \"<span class=sf-dump-key>break_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Short</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546246184\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-9968727 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IlNvMStoeC9pR3JrVjhKRVlncytzL3c9PSIsInZhbHVlIjoid0t6OXZkZS9XcGRLMG1hN3AwQjJJSVV6ZVBhakhzM1RBTnAwQldYV0J1S2tvOEdZbVo1S3Z5TTBvOC9xK3RsajN3QVl6Qk8zS0NtS2VYVjV3NmVEZTNvcGo2eU0wNFFjYlM1MjREcXdRK0k4Mm02UXl2QUpGZU5qQ2pzQmI5YVoiLCJtYWMiOiIzYmE2MDQ2MjQwODY1NDg0YThhNDUyZjhlODU2OWMxMDc5YjdjZjUyZDY4ZTU2MTI3MjhlOGY1MzlkOTFhYWFjIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6Ik1wNFcrNGlmalA0Vm9kaHduRFE0bXc9PSIsInZhbHVlIjoiRUdRM2NxUDJMazhNQmh3SWtvLys5RUI0UjZ1aXRvZG5McTBjblRYL0dxQThEL2JDckROdkFka0dLU29yUTd3UC90OURsODZHVXVTaHFoOEtld2FiblhxanRNVStGRDhKcFdtUEhDY05KT1pXZzRBNnc4dVBYNW9aWU5rZmErNm8iLCJtYWMiOiIzOTk3NzFmNjhkMTAwZmRiM2U4ZjcxOWVmNDE1YWIxNzI5OWY0ZjcxNDY4YzM0NzZiZjBiMjhhZGNhODdmYWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9968727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2005430379 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMKoX9Ud84y73OOkpPYPquCdcjjSNXP25PlIGZUu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005430379\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1824392667 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 08:54:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824392667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1152284783 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1748856962</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"439 characters\">{&quot;title&quot;:&quot;Break Started Successfully.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152284783\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/daily_break/start", "action_name": "administration.daily_break.start", "controller_action": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak"}, "badge": "302 Found"}}