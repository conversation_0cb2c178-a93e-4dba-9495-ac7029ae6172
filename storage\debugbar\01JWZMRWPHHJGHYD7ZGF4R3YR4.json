{"__meta": {"id": "01JWZMRWPHHJGHYD7ZGF4R3YR4", "datetime": "2025-06-05 14:54:24", "utime": **********.212103, "method": "POST", "uri": "/daily_break/start", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749113662.291785, "end": **********.212144, "duration": 1.9203588962554932, "duration_str": "1.92s", "measures": [{"label": "Booting", "start": 1749113662.291785, "relative_start": 0, "end": **********.828594, "relative_end": **********.828594, "duration": 1.***************, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.828617, "relative_start": 1.****************, "end": **********.212148, "relative_end": 4.0531158447265625e-06, "duration": 0.***************, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.866991, "relative_start": 1.****************, "end": **********.877307, "relative_end": **********.877307, "duration": 0.010315895080566406, "duration_str": "10.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.204528, "relative_start": 1.***************, "end": **********.206004, "relative_end": **********.206004, "duration": 0.0014758110046386719, "duration_str": "1.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST daily_break/start", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Daily Break Create", "controller": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.daily_break.start", "prefix": "/daily_break", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php:75-90</a>"}, "queries": {"count": 14, "nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025069999999999995, "accumulated_duration_str": "25.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.998818, "duration": 0.00848, "duration_str": "8.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 33.825}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.025068, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 33.825, "width_percent": 2.992}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.040139, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 36.817, "width_percent": 3.071}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.046726, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 39.888, "width_percent": 3.151}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0672581, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 43.039, "width_percent": 4.587}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.0769148, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 47.627, "width_percent": 3.111}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.105777, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 50.738, "width_percent": 6.781}, {"sql": "select * from `attendances` where `user_id` = 1 and `type` = 'Regular' and `clock_out` is null and `attendances`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, "Regular"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 23}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.122687, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:23", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=23", "ajax": false, "filename": "BreakStartStopService.php", "line": "23"}, "connection": "blueorange", "explain": null, "start_percent": 57.519, "width_percent": 7.02}, {"sql": "select * from `employee_shifts` where `employee_shifts`.`user_id` = 1 and `employee_shifts`.`user_id` is not null and `status` = 'active' and `employee_shifts`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "active"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 69}, {"index": 25, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 135}, {"index": 26, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 33}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1335158, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=69", "ajax": false, "filename": "UserAccessors.php", "line": "69"}, "connection": "blueorange", "explain": null, "start_percent": 64.539, "width_percent": 5.943}, {"sql": "select * from `attendances` where `attendances`.`user_id` = 1 and `attendances`.`user_id` is not null and `type` = 'Regular' and `clock_out` is null and `attendances`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [1, "Regular"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 143}, {"index": 20, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.141349, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:143", "source": {"index": 19, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=143", "ajax": false, "filename": "BreakStartStopService.php", "line": "143"}, "connection": "blueorange", "explain": null, "start_percent": 70.483, "width_percent": 7.06}, {"sql": "select count(*) as aggregate from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `type` = 'Short' and `daily_breaks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 5074, "Short"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 181}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.150516, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:181", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=181", "ajax": false, "filename": "BreakStartStopService.php", "line": "181"}, "connection": "blueorange", "explain": null, "start_percent": 77.543, "width_percent": 7.659}, {"sql": "select count(*) as aggregate from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `type` = 'Long' and `daily_breaks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 5074, "<PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 186}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 36}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.160155, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:186", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=186", "ajax": false, "filename": "BreakStartStopService.php", "line": "186"}, "connection": "blueorange", "explain": null, "start_percent": 85.201, "width_percent": 4.747}, {"sql": "select * from `daily_breaks` where `user_id` = 1 and `attendance_id` = 5074 and `daily_breaks`.`deleted_at` is null order by `break_out_at` desc limit 1", "type": "query", "params": [], "bindings": [1, 5074], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, {"index": 17, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 39}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.167322, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:202", "source": {"index": 16, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=202", "ajax": false, "filename": "BreakStartStopService.php", "line": "202"}, "connection": "blueorange", "explain": null, "start_percent": 89.948, "width_percent": 5.903}, {"sql": "select * from `users` where `users`.`id` in (1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, {"index": 22, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 39}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController.php", "line": 83}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.180029, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "BreakStartStopService.php:202", "source": {"index": 21, "namespace": null, "name": "app/Services/Administration/DailyBreak/BreakStartStopService.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\DailyBreak\\BreakStartStopService.php", "line": 202}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FDailyBreak%2FBreakStartStopService.php&line=202", "ajax": false, "filename": "BreakStartStopService.php", "line": "202"}, "connection": "blueorange", "explain": null, "start_percent": 95.852, "width_percent": 4.148}]}, "models": {"data": {"App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Attendance\\Attendance": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FAttendance%2FAttendance.php&line=1", "ajax": false, "filename": "Attendance.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\EmployeeShift\\EmployeeShift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FEmployeeShift%2FEmployeeShift.php&line=1", "ajax": false, "filename": "EmployeeShift.php", "line": "?"}}, "App\\Models\\DailyBreak\\DailyBreak": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FDailyBreak%2FDailyBreak.php&line=1", "ajax": false, "filename": "DailyBreak.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Daily Break Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-506022105 data-indent-pad=\"  \"><span class=sf-dump-note>Daily Break Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Daily Break Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-506022105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.116686, "xdebug_link": null}]}, "session": {"_token": "WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/daily_break/start_stop\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:12 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.heightAuto\"\n    5 => \"alert.config.padding\"\n    6 => \"alert.config.showConfirmButton\"\n    7 => \"alert.config.showCloseButton\"\n    8 => \"alert.config.timerProgressBar\"\n    9 => \"alert.config.customClass\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config\"\n  ]\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1748856962\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Oops! Error.\",\"text\":\"You cannot take another break within 1 hour of your previous break.\",\"timer\":\"5000\",\"width\":\"32rem\",\"heightAuto\":true,\"padding\":\"1.25rem\",\"showConfirmButton\":true,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"icon\":\"error\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "array:2 [\n  \"01JWZMRVCHG8TM8DASTM59AM62\" => null\n  \"01JWZMRVY3KPF33PB365KJP5J0\" => null\n]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/daily_break/start", "action_name": "administration.daily_break.start", "controller_action": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak", "uri": "POST daily_break/start", "controller": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/daily_break", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDailyBreak%2FDailyBreakController.php&line=75\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/DailyBreak/DailyBreakController.php:75-90</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Daily Break Create", "duration": "1.93s", "peak_memory": "28MB", "response": "Redirect to https://blueorange.test/daily_break/start_stop", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-927756344 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-927756344\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2004500629 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>userid</span>\" => \"<span class=sf-dump-str title=\"11 characters\">UID00000001</span>\"\n  \"<span class=sf-dump-key>break_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Long</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004500629\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-920996152 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6Imt0eGdOM0I3TFdFemJ4RUJXTWdMRHc9PSIsInZhbHVlIjoiYlJUZFAxdGRXTGtOMmYwcnhXYklIeWI3TEtzOUg0WWgwU0MwbFY4R21vWk5VN0hUNTFMRFVUenZTOFlFMmJwWmlxb0tQakNUVVUrSUJTMzNsUW5wYUxLRDVwOHB5cXFhaEpyNEEzU1R2Uk5oVTgzZDJNWmU1OTNQRjlHMnFvS2oiLCJtYWMiOiI5OGZlMDFlMTc1ODQzN2U5OTI5YjUxMzc5ODkzOWQyYWRhNzUwODcyNmM2OTBhMzE1MGI1YTUxNWUyZGM3ZmRjIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6InJEc1g0YXNTemptQ0pQZStoZE5oM3c9PSIsInZhbHVlIjoidVUrMHVyWUkxVTNMbXZqcTM1TE9aUWZpSXZwNytKampoVWtLODVaY2x5Wi9mUkloVm9wUDlaTTNxNmdJZWROaEY1VUY0UXdmKytJZFo3UE5jTm9LMjlwT3lvN3U5aE9PZ01TU1hmdjVIUDIzaTRvb3hoejdEclBkMFBhL2o3OG0iLCJtYWMiOiJjN2UyOThkMTE5YWIxM2NmNjM0YWMxYjBjMDkxMGViMTJmYjJiZWJmZGViYjQ2MGUwOTRmYmNlOGIxMWJmZjZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920996152\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1369499515 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMKoX9Ud84y73OOkpPYPquCdcjjSNXP25PlIGZUu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369499515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-94804595 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 05 Jun 2025 08:54:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94804595\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650343295 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDAjBxMALh5BfOt2JbS9j49bVm0s2zu8A3OYeuvZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">https://blueorange.test/daily_break/start_stop</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">alert.config.heightAuto</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1748856962</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"472 characters\">{&quot;title&quot;:&quot;Oops! Error.&quot;,&quot;text&quot;:&quot;You cannot take another break within 1 hour of your previous break.&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;heightAuto&quot;:true,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:true,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;icon&quot;:&quot;error&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JWZMRVCHG8TM8DASTM59AM62</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01JWZMRVY3KPF33PB365KJP5J0</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650343295\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/daily_break/start", "action_name": "administration.daily_break.start", "controller_action": "App\\Http\\Controllers\\Administration\\DailyBreak\\DailyBreakController@startBreak"}, "badge": "302 Found"}}